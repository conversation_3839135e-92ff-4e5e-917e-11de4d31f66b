import React, { useEffect, useRef, useState } from 'react';
import * as styles from "./index.module.less";
import { useAIProcess } from '../../hooks/useAIProcess';
import { getAIService } from '../../config/aiConfig';

interface AIProcessModalProps {
  isVisible: boolean;
  selectedText: string;
  actionType: string;
  onClose: () => void;
  position?: { x: number; y: number };
}

const AIProcessModal: React.FC<AIProcessModalProps> = ({
  isVisible,
  selectedText,
  actionType,
  onClose,
  position = { x: 0, y: 0 }
}) => {
  const { state, startProcess, stopProcess, resetState } = useAIProcess();
  const modalRef = useRef<HTMLDivElement>(null);
  const aiService = getAIService();
  const processStartedRef = useRef<string>(''); // 记录已启动的处理
  const [isThinkingExpanded, setIsThinkingExpanded] = useState(false); // 思考过程展开状态
  const [showContinueInput, setShowContinueInput] = useState(false); // 是否显示继续问输入框
  const [continueQuestion, setContinueQuestion] = useState(''); // 继续问的内容
  const [isClosing, setIsClosing] = useState(false); // 是否正在关闭（用于淡出动画）

  // 获取操作类型的中文名称
  const getActionName = (action: string): string => {
    return aiService.getActionName(action);
  };

  // 处理关闭
  const handleClose = () => {
    setIsClosing(true);
    // 延迟执行关闭，让淡出动画播放
    setTimeout(() => {
      stopProcess();
      resetState();
      setIsClosing(false);
      onClose();
    }, 200); // 与CSS动画时长匹配
  };



  // 继续问
  const handleContinueAsk = () => {
    console.log('handleContinueAsk');
    setShowContinueInput(true);
  };

  // 发送继续问题
  const handleSendContinueQuestion = () => {
    if (!continueQuestion.trim()) return;

    // 向背景脚本发送消息，请求打开侧边栏并传递问题
    chrome.runtime.sendMessage({
      action: 'continue-ask',
      question: continueQuestion.trim(),
      originalText: selectedText,
      originalAction: actionType
    });

    // 关闭弹窗
    handleClose();
  };

  // 处理输入框回车事件
  const handleContinueInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendContinueQuestion();
    }
  };

  // 调整
  const handleAdjust = () => {
    // TODO: 实现调整功能 - 可以让用户修改参数重新生成
    console.log('调整功能');
  };

  // 弃用
  const handleDiscard = () => {
    // 关闭弹窗，不保存结果
    handleClose();
  };

  // 插入到下方
  const handleInsertBelow = () => {
    // TODO: 实现插入到下方功能 - 在原文下方插入生成的内容
    console.log('插入到下方:', state.content);
    handleClose();
  };

  // 替换原文
  const handleReplace = () => {
    // TODO: 实现替换原文功能 - 用生成的内容替换选中的原文
    console.log('替换原文:', state.content);
    handleClose();
  };

  // 当组件显示时开始处理
  useEffect(() => {
    const processKey = `${actionType}-${selectedText}`;

    if (isVisible && selectedText && actionType && processStartedRef.current !== processKey) {
      processStartedRef.current = processKey;
      startProcess(actionType, selectedText);
    }

    return () => {
      if (!isVisible) {
        processStartedRef.current = '';
        stopProcess();
      }
    };
  }, [isVisible, selectedText, actionType, startProcess, stopProcess]);

  // 点击外部关闭
  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     console.log('event.target', event.target,modalRef.current ,!modalRef.current.contains(event.target as Node));
      
  //     if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
  //       handleClose();
  //     }
  //   };

  //   if (isVisible) {
  //     document.addEventListener('mousedown', handleClickOutside);
  //   }

  //   return () => {
  //     document.removeEventListener('mousedown', handleClickOutside);
  //   };
  // }, [isVisible]);

  if (!isVisible && !isClosing) return null;

  return (
    <div
      className={`${styles.modalOverlay} ${isClosing ? styles.closing : ''}`}
      onClick={(e) => {
        // 只有当点击的是overlay本身时才关闭弹窗
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className={`${styles.modal} ${isClosing ? styles.closing : ''}`}
        style={{
          left: position.x,
          top: position.y,
        }}
        onClick={(e) => {
          // 统一在modal容器级别阻止事件冒泡到overlay
          // 这样点击modal内的任何内容都不会关闭弹窗
          e.stopPropagation();
        }}
      >
        {/* 头部 */}
        <div className={styles.modalHeader}>
          <div className={styles.headerLeft}>
            <div className={styles.aiIcon}>AI</div>
            <span className={styles.actionTitle}>{getActionName(actionType)}</span>
          </div>
          <button
            className={styles.closeButton}
            onClick={handleClose}
          >
            ×
          </button>
        </div>

        {/* 思考过程 - 可折叠 */}
        <div className={styles.thinkingSection}>
          <button
            className={styles.thinkingToggle}
            onClick={() => setIsThinkingExpanded(!isThinkingExpanded)}
          >
            <span className={styles.thinkingText}>
              已深度思考 (用时 {state.processingTime} 秒)
            </span>
            <span className={`${styles.expandIcon} ${isThinkingExpanded ? styles.expanded : ''}`}>
              {isThinkingExpanded ? '∧' : '∨'}
            </span>
          </button>

          {isThinkingExpanded && (
            <div className={styles.thinkingContent}>
              <p>嗯，用户要求我作为专业改写专家，将一段中文内容改得更正式官方。让我看看</p>
              <p>原文："{selectedText.slice(0, 50)}{selectedText.length > 50 ? '...' : ''}"</p>
              <p>这句子很有诗意啊，"乌衣巷"是南京的历史名巷，"王羲之"是书圣，"墨韵风骨"</p>
            </div>
          )}
        </div>

        {/* 错误状态 */}
        {state.error && (
          <div className={styles.errorMessage}>
            处理失败：{state.error}
          </div>
        )}

        {/* 主要内容区域 */}
        <div className={styles.contentArea}>
          {state.content && (
            <div className={styles.resultContent}>
              {state.content}
              {state.isProcessing && <span className={styles.cursor}>|</span>}
            </div>
          )}

          {state.isComplete && state.content && (
            <div className={styles.contentFooter}>
              <span className={styles.contentStats}>已生成内容 {state.content.length} 字</span>
            </div>
          )}
        </div>

        {/* 继续问输入框 */}
        {showContinueInput && (
          <div className={styles.continueInputSection}>
            <div className={styles.continueInputHeader}>
              <span className={styles.continueInputTitle}>继续提问</span>
              <button
                className={styles.continueInputClose}
                onClick={() => setShowContinueInput(false)}
              >
                ×
              </button>
            </div>
            <div className={styles.continueInputContainer}>
              <textarea
                className={styles.continueInput}
                value={continueQuestion}
                onChange={(e) => setContinueQuestion(e.target.value)}
                onKeyDown={handleContinueInputKeyDown}
                placeholder="请输入您想继续询问的问题..."
                rows={3}
                autoFocus
              />
              <div className={styles.continueInputActions}>
                <button
                  className={styles.continueInputCancel}
                  onClick={() => setShowContinueInput(false)}
                >
                  取消
                </button>
                <button
                  className={styles.continueInputSend}
                  onClick={handleSendContinueQuestion}
                  disabled={!continueQuestion.trim()}
                >
                  发送
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 底部操作按钮 */}
        {state.isComplete && !state.error && !showContinueInput && (
          <div className={styles.actionButtons}>
            <button
              className={styles.actionBtn}
              onClick={handleContinueAsk}
            >
              继续问
            </button>
            <button
              className={styles.actionBtn}
              onClick={handleAdjust}
            >
              调整
            </button>
            <button
              className={styles.actionBtn}
              onClick={handleDiscard}
            >
              弃用
            </button>
            <button
              className={styles.actionBtn}
              onClick={handleInsertBelow}
            >
              插入到下方
            </button>
            <button
              className={styles.primaryBtn}
              onClick={handleReplace}
            >
              <span className={styles.buttonIcon}>✓</span>
              替换原文
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIProcessModal;
